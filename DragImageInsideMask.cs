using UnityEngine;
using UnityEngine.EventSystems;

public class DragImageInsideMask : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IDragHandler
{
    public RectTransform imageToDrag; // Assign the TargetImage's RectTransform
    public RectTransform maskRect;    // Assign the MaskHolder's RectTransform

    private Vector2 dragLimit;

    void Start()
    {
        // Compute the drag limits once (optional, can be improved)
        Vector2 maskSize = maskRect.rect.size;
        Vector2 imageSize = imageToDrag.rect.size;
        dragLimit = (imageSize - maskSize) / 2f;
    }

    public void OnDrag(PointerEventData eventData)
    {
        Vector2 newPos = imageToDrag.anchoredPosition + eventData.delta;

        // Clamp position to keep image within bounds
        newPos.x = Mathf.Clamp(newPos.x, -dragLimit.x, dragLimit.x);
        newPos.y = Mathf.Clamp(newPos.y, -dragLimit.y, dragLimit.y);

        imageToDrag.anchoredPosition = newPos;
    }
}
