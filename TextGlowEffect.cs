using UnityEngine;
using UnityEngine.UI;

public class TextGlowEffect : MonoBehaviour
{
    [SerializeField] private float glowSpeed = 1f;
    [SerializeField] private float glowWidth = 0.1f;
    [SerializeField] private Color glowColor = Color.white;
    [SerializeField] private float startDelay = 0f;

    private Material material;
    private float currentPosition = -1f;
    private float delayTimer = 0f;
    private bool effectStarted = false;

    void Start()
    {
        Image image = GetComponent<Image>();
        material = new Material(Shader.Find("Custom/TextGlow"));
        image.material = material;
        
        material.SetFloat("_GlowWidth", glowWidth);
        material.SetColor("_GlowColor", glowColor);
    }

    void Update()
    {
        if (!effectStarted)
        {
            delayTimer += Time.deltaTime;
            if (delayTimer >= startDelay)
            {
                effectStarted = true;
            }
            else
            {
                return; // Don't update the glow effect yet
            }
        }

        currentPosition += Time.deltaTime * glowSpeed;
        if (currentPosition > 2f) currentPosition = -1f;

        material.SetFloat("_GlowPosition", currentPosition);
    }
}