using UnityEngine;
using UnityEngine.UI;
 
public class SineWaveController : MonoBehaviour
{
    public LineRenderer targetWave;
    public LineRenderer playerWave;

    public float matchTolerance = 0.05f;
    public GameObject waveSuccessObject;

    private float playerFreq = 1f, playerMag = 1f;
    private float targetFreq = 1.5f, targetMag = 0.8f;

    private int resolution = 100;
    private float waveLength = 2 * Mathf.PI;

    void Start()
    {
        DrawWave(targetWave, targetFreq, targetMag);
        DrawWave(playerWave, playerFreq, playerMag);
    }

    public void AdjustFrequency(float delta)
    {
        playerFreq += delta;
        RedrawPlayerWave();
    }

    public void AdjustMagnitude(float delta)
    {
        playerMag += delta;
        RedrawPlayerWave();
    }

    void RedrawPlayerWave()
    {
        DrawWave(playerWave, playerFreq, playerMag);
        CheckMatch();
    }

    void DrawWave(LineRenderer lr, float freq, float mag)
    {
        lr.positionCount = resolution;
        for (int i = 0; i < resolution; i++)
        {
            float x = i * waveLength / resolution;
            float y = Mathf.Sin(x * freq) * mag;
            lr.SetPosition(i, new Vector3(x, y, 0));
        }
    }

    void CheckMatch()
    {
        if (Mathf.Abs(playerFreq - targetFreq) <= matchTolerance &&
            Mathf.Abs(playerMag - targetMag) <= matchTolerance)
        {
            waveSuccessObject.SetActive(true);
            GaugeGameProgression.Instance.CompletePart(1);
        }
        else
        {
            waveSuccessObject.SetActive(false);
        }
    }
}
