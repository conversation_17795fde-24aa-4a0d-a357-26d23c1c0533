using UnityEngine;
using UnityEngine.UI;

public class SineWaveController : MonoBehaviour
{
    [Header("UI Line Renderers")]
    public Component targetWave;  // UILineRenderer component
    public Component playerWave;  // UILineRenderer component

    public float matchTolerance = 0.05f;
    public GameObject waveSuccessObject;

    [Header("Knob Controls")]
    public RotatingKnob frequencyKnob;
    public RotatingKnob magnitudeKnob;

    [Header("Value Ranges")]
    public float minFrequency = 0.5f;
    public float maxFrequency = 3f;
    public float minMagnitude = 0.2f;
    public float maxMagnitude = 2f;

    private float playerFreq = 1f, playerMag = 1f;
    private float targetFreq = 1.5f, targetMag = 0.8f;

    private int resolution = 100;
    private float waveLength = 2 * Mathf.PI;

    void Start()
    {
        // Randomize target values
        targetFreq = Random.Range(minFrequency, maxFrequency);
        targetMag = Random.Range(minMagnitude, maxMagnitude);

        DrawWave(targetWave, targetFreq, targetMag);
        DrawWave(playerWave, playerFreq, playerMag);

        // Set up knob listeners
        if (frequencyKnob != null)
        {
            frequencyKnob.OnValueChanged.AddListener(OnFrequencyKnobChanged);
            frequencyKnob.SetValue(0.5f); // Start at middle position
        }

        if (magnitudeKnob != null)
        {
            magnitudeKnob.OnValueChanged.AddListener(OnMagnitudeKnobChanged);
            magnitudeKnob.SetValue(0.5f); // Start at middle position
        }
    }

    public void OnFrequencyKnobChanged(float knobValue)
    {
        // Convert knob value (0-1) to frequency range
        playerFreq = Mathf.Lerp(minFrequency, maxFrequency, knobValue);
        RedrawPlayerWave();
    }

    public void OnMagnitudeKnobChanged(float knobValue)
    {
        // Convert knob value (0-1) to magnitude range
        playerMag = Mathf.Lerp(minMagnitude, maxMagnitude, knobValue);
        RedrawPlayerWave();
    }

    public void AdjustFrequency(float delta)
    {
        playerFreq += delta;
        RedrawPlayerWave();
    }

    public void AdjustMagnitude(float delta)
    {
        playerMag += delta;
        RedrawPlayerWave();
    }

    void RedrawPlayerWave()
    {
        DrawWave(playerWave, playerFreq, playerMag);
        CheckMatch();
    }

    void DrawWave(Component lr, float freq, float mag)
    {
        if (lr == null) return;

        // Get the RectTransform for UI positioning
        RectTransform rectTransform = lr.GetComponent<RectTransform>();
        if (rectTransform == null) return;

        // Create points array
        Vector2[] points = new Vector2[resolution];

        // Calculate wave points for UI space
        for (int i = 0; i < resolution; i++)
        {
            float x = i * waveLength / resolution;
            float y = Mathf.Sin(x * freq) * mag;

            // Convert to UI coordinates (normalized to the RectTransform)
            Vector2 point = new Vector2(
                (x / waveLength) * rectTransform.rect.width - rectTransform.rect.width * 0.5f,
                y * rectTransform.rect.height * 0.25f // Scale magnitude to fit UI
            );

            points[i] = point;
        }

        // Try different common UILineRenderer property names using reflection
        var lrType = lr.GetType();

        // Try "Points" property (most common)
        var pointsProperty = lrType.GetProperty("Points");
        if (pointsProperty != null && pointsProperty.CanWrite)
        {
            pointsProperty.SetValue(lr, points);
        }
        else
        {
            // Try "points" field
            var pointsField = lrType.GetField("points");
            if (pointsField != null)
            {
                pointsField.SetValue(lr, points);
            }
            else
            {
                // Try "m_points" field
                var mPointsField = lrType.GetField("m_points");
                if (mPointsField != null)
                {
                    mPointsField.SetValue(lr, points);
                }
            }
        }

        // Try to refresh the line renderer
        var setVerticesDirtyMethod = lrType.GetMethod("SetVerticesDirty");
        if (setVerticesDirtyMethod != null)
        {
            setVerticesDirtyMethod.Invoke(lr, null);
        }
        else
        {
            // Try alternative refresh methods
            var refreshMethod = lrType.GetMethod("Refresh");
            if (refreshMethod != null)
            {
                refreshMethod.Invoke(lr, null);
            }
        }
    }

    void CheckMatch()
    {
        if (Mathf.Abs(playerFreq - targetFreq) <= matchTolerance &&
            Mathf.Abs(playerMag - targetMag) <= matchTolerance)
        {
            waveSuccessObject.SetActive(true);
            GaugeGameProgression.Instance.CompletePart(1);
        }
        else
        {
            waveSuccessObject.SetActive(false);
        }
    }

    // Alternative method if your UILineRenderer has different property names
    // You can modify this method to match your specific UILineRenderer implementation
    void DrawWaveAlternative(Component lr, float freq, float mag)
    {
        if (lr == null) return;

        // Example for a common UILineRenderer implementation:
        // Uncomment and modify these lines based on your UILineRenderer's API

        /*
        var uiLineRenderer = lr as UILineRenderer; // Cast to your specific type
        if (uiLineRenderer != null)
        {
            List<Vector2> points = new List<Vector2>();
            RectTransform rectTransform = lr.GetComponent<RectTransform>();

            for (int i = 0; i < resolution; i++)
            {
                float x = i * waveLength / resolution;
                float y = Mathf.Sin(x * freq) * mag;

                Vector2 point = new Vector2(
                    (x / waveLength) * rectTransform.rect.width - rectTransform.rect.width * 0.5f,
                    y * rectTransform.rect.height * 0.25f
                );

                points.Add(point);
            }

            uiLineRenderer.Points = points.ToArray(); // or whatever property your UILineRenderer uses
            uiLineRenderer.SetVerticesDirty(); // or whatever refresh method it uses
        }
        */
    }
}
