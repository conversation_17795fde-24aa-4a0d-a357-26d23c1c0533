using UnityEngine;
using UnityEngine.UI;

public class SineWaveController : MonoBehaviour
{
    [Header("UI Sine Wave Components")]
    public UISineWave targetWave;
    public UISineWave playerWave;

    public float matchTolerance = 0.05f;
    public GameObject waveSuccessObject;

    [Header("Knob Controls")]
    public RotatingKnob frequencyKnob;
    public RotatingKnob magnitudeKnob;

    [Header("Value Ranges")]
    public float minFrequency = 0.5f;
    public float maxFrequency = 3f;
    public float minMagnitude = 0.2f;
    public float maxMagnitude = 2f;

    private float playerFreq = 1f, playerMag = 1f;
    private float targetFreq = 1.5f, targetMag = 0.8f;

    private int resolution = 100;
    private float waveLength = 2 * Mathf.PI;
    private bool isLocked = false;

    void Start()
    {
        // Randomize target values
        targetFreq = Random.Range(minFrequency, maxFrequency);
        targetMag = Random.Range(minMagnitude, maxMagnitude);

        // Set up the waves
        if (targetWave != null)
        {
            targetWave.SetWaveProperties(targetFreq, targetMag);
            targetWave.SetColor(Color.red); // Target wave in red
            targetWave.SetAnimated(true);
            targetWave.SetAnimationSpeed(0.5f); // Slower animation for target
        }

        if (playerWave != null)
        {
            playerWave.SetWaveProperties(playerFreq, playerMag);
            playerWave.SetColor(Color.blue); // Player wave in blue
            playerWave.SetAnimated(true);
            playerWave.SetAnimationSpeed(1f); // Normal animation speed
        }

        // Set up knob listeners
        if (frequencyKnob != null)
        {
            frequencyKnob.OnValueChanged.AddListener(OnFrequencyKnobChanged);
            frequencyKnob.SetValue(0.5f); // Start at middle position
        }

        if (magnitudeKnob != null)
        {
            magnitudeKnob.OnValueChanged.AddListener(OnMagnitudeKnobChanged);
            magnitudeKnob.SetValue(0.5f); // Start at middle position
        }

        Debug.Log($"Target Wave - Frequency: {targetFreq:F2}, Magnitude: {targetMag:F2}");
    }

    public void OnFrequencyKnobChanged(float knobValue)
    {
        if (isLocked) return; // Don't allow changes when locked

        // Convert knob value (0-1) to frequency range
        playerFreq = Mathf.Lerp(minFrequency, maxFrequency, knobValue);
        UpdatePlayerWave();
    }

    public void OnMagnitudeKnobChanged(float knobValue)
    {
        if (isLocked) return; // Don't allow changes when locked

        // Convert knob value (0-1) to magnitude range
        playerMag = Mathf.Lerp(minMagnitude, maxMagnitude, knobValue);
        UpdatePlayerWave();
    }

    public void AdjustFrequency(float delta)
    {
        if (isLocked) return; // Don't allow changes when locked

        playerFreq = Mathf.Clamp(playerFreq + delta, minFrequency, maxFrequency);
        UpdatePlayerWave();
    }

    public void AdjustMagnitude(float delta)
    {
        if (isLocked) return; // Don't allow changes when locked

        playerMag = Mathf.Clamp(playerMag + delta, minMagnitude, maxMagnitude);
        UpdatePlayerWave();
    }

    void UpdatePlayerWave()
    {
        if (playerWave != null)
        {
            playerWave.SetWaveProperties(playerFreq, playerMag);
        }
        CheckMatch();
    }

    void CheckMatch()
    {
        if (isLocked) return; // Don't check if already locked

        bool frequencyMatch = Mathf.Abs(playerFreq - targetFreq) <= matchTolerance;
        bool magnitudeMatch = Mathf.Abs(playerMag - targetMag) <= matchTolerance;

        Debug.Log($"Player: F={playerFreq:F2}, M={playerMag:F2} | Target: F={targetFreq:F2}, M={targetMag:F2} | Match: F={frequencyMatch}, M={magnitudeMatch}");

        if (frequencyMatch && magnitudeMatch)
        {
            LockWaves();
            waveSuccessObject.SetActive(true);
            GaugeGameProgression.Instance.CompletePart(1);
            Debug.Log("Sine waves matched and locked!");
        }
        else
        {
            waveSuccessObject.SetActive(false);
        }
    }

    void LockWaves()
    {
        isLocked = true;

        // Lock the player wave
        if (playerWave != null)
        {
            playerWave.SetLocked(true);
        }

        // Disable knob interactions
        if (frequencyKnob != null)
        {
            frequencyKnob.enabled = false;
        }

        if (magnitudeKnob != null)
        {
            magnitudeKnob.enabled = false;
        }

        Debug.Log("Waves locked - no more adjustments allowed!");
    }

    // Public methods to reset the mini-game
    public void ResetWaves()
    {
        // Unlock everything first
        UnlockWaves();

        // Generate new random target values
        targetFreq = Random.Range(minFrequency, maxFrequency);
        targetMag = Random.Range(minMagnitude, maxMagnitude);

        // Reset player values to middle
        playerFreq = (minFrequency + maxFrequency) * 0.5f;
        playerMag = (minMagnitude + maxMagnitude) * 0.5f;

        // Update waves
        if (targetWave != null)
        {
            targetWave.SetWaveProperties(targetFreq, targetMag);
            targetWave.SetAnimated(true);
        }

        if (playerWave != null)
        {
            playerWave.SetWaveProperties(playerFreq, playerMag);
            playerWave.SetAnimated(true);
        }

        // Reset knobs
        if (frequencyKnob != null)
        {
            frequencyKnob.SetValueWithoutNotify(0.5f);
        }

        if (magnitudeKnob != null)
        {
            magnitudeKnob.SetValueWithoutNotify(0.5f);
        }

        waveSuccessObject.SetActive(false);
        Debug.Log($"Waves reset - New target: F={targetFreq:F2}, M={targetMag:F2}");
    }

    public void UnlockWaves()
    {
        isLocked = false;

        // Unlock the player wave
        if (playerWave != null)
        {
            playerWave.SetLocked(false);
            playerWave.SetColor(Color.blue); // Restore original color
        }

        // Re-enable knob interactions
        if (frequencyKnob != null)
        {
            frequencyKnob.enabled = true;
        }

        if (magnitudeKnob != null)
        {
            magnitudeKnob.enabled = true;
        }

        Debug.Log("Waves unlocked - adjustments allowed again!");
    }

    // Get current wave properties for debugging
    public void GetWaveProperties(out float playerF, out float playerM, out float targetF, out float targetM)
    {
        playerF = playerFreq;
        playerM = playerMag;
        targetF = targetFreq;
        targetM = targetMag;
    }

    // Check if waves are currently locked
    public bool IsLocked()
    {
        return isLocked;
    }
}
