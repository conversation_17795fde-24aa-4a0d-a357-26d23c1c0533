using UnityEngine;
using UnityEngine.UI;

public class GaugeGameProgression : MonoBehaviour
{
    public static GaugeGameProgression Instance;

    public Button completionButton;
    public GameObject activateA, activateB;
    public GameObject deactivateA, deactivateB;

    private bool part1Done = false;
    private bool part2Done = false;

    void Awake()
    {
        Instance = this;
        completionButton.interactable = false;
    }

    public void CompletePart(int part)
    {
        if (part == 1) part1Done = true;
        if (part == 2) part2Done = true;

        if (part1Done && part2Done)
        {
            completionButton.interactable = true;
        }
    }

    public void OnCompletionButtonClick()
    {
        activateA.SetActive(true);
        activateB.SetActive(true);
        deactivateA.SetActive(false);
        deactivateB.SetActive(false);
    }
}
