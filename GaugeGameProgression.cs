using UnityEngine;
using UnityEngine.UI;

public class GaugeGameProgression : MonoBehaviour
{
    public static GaugeGameProgression Instance;

    public Button completionButton;
    public GameObject bothPartsCompletedObject; // New object to activate when both parts are done
    public GameObject activateA, activateB;
    public GameObject deactivateA, deactivateB;

    private bool part1Done = false;
    private bool part2Done = false;

    void Awake()
    {
        Instance = this;
        completionButton.interactable = false;

        // Make sure the both parts completed object starts inactive
        if (bothPartsCompletedObject != null)
        {
            bothPartsCompletedObject.SetActive(false);
        }
    }

    public void CompletePart(int part)
    {
        if (part == 1) part1Done = true;
        if (part == 2) part2Done = true;

        if (part1Done && part2Done)
        {
            // Enable the completion button
            completionButton.interactable = true;

            // Activate the both parts completed object
            if (bothPartsCompletedObject != null)
            {
                bothPartsCompletedObject.SetActive(true);
            }

            Debug.Log("Both mini-game parts completed! Completion button enabled and special object activated.");
        }
    }

    public void OnCompletionButtonClick()
    {
        activateA.SetActive(true);
        activateB.SetActive(true);
        deactivateA.SetActive(false);
        deactivateB.SetActive(false);
    }
}
