using UnityEngine;
using UnityEngine.UI;

public class GaugeGameProgression : MonoBehaviour
{
    public static GaugeGameProgression Instance;

    public Button completionButton;
    public GameObject bothPartsCompletedObject; // New object to activate when both parts are done
    public GameObject activateA, activateB;
    public GameObject deactivateA, deactivateB;

    private bool part1Done = false;
    private bool part2Done = false;

    void Awake()
    {
        Instance = this;

        if (completionButton != null)
        {
            completionButton.interactable = false;
            Debug.Log("Completion button found and set to non-interactable");
        }
        else
        {
            Debug.LogError("Completion button is not assigned!");
        }

        // Make sure the both parts completed object starts inactive
        if (bothPartsCompletedObject != null)
        {
            bothPartsCompletedObject.SetActive(false);
        }
    }

    void Start()
    {
        // Verify button listener is set up
        if (completionButton != null)
        {
            // Check if the button has any listeners
            var buttonClickEvent = completionButton.onClick;
            Debug.Log($"Completion button has {buttonClickEvent.GetPersistentEventCount()} persistent listeners");

            // You may need to manually add the listener if it's not set in the inspector
            // Uncomment the line below if the button doesn't have the listener set up:
            // completionButton.onClick.AddListener(OnCompletionButtonClick);
        }
    }

    public void CompletePart(int part)
    {
        if (part == 1) part1Done = true;
        if (part == 2) part2Done = true;

        if (part1Done && part2Done)
        {
            // Enable the completion button
            if (completionButton != null)
            {
                completionButton.interactable = true;
                Debug.Log("Completion button enabled!");
            }

            // Activate the both parts completed object
            if (bothPartsCompletedObject != null)
            {
                bothPartsCompletedObject.SetActive(true);
            }

            Debug.Log("Both mini-game parts completed! Completion button enabled and special object activated.");
        }
    }

    public void OnCompletionButtonClick()
    {
        Debug.Log("OnCompletionButtonClick() called!");

        if (activateA != null)
        {
            activateA.SetActive(true);
            Debug.Log("Activated object A");
        }
        else
        {
            Debug.LogWarning("activateA is null!");
        }

        if (activateB != null)
        {
            activateB.SetActive(true);
            Debug.Log("Activated object B");
        }
        else
        {
            Debug.LogWarning("activateB is null!");
        }

        if (deactivateA != null)
        {
            deactivateA.SetActive(false);
            Debug.Log("Deactivated object A");
        }
        else
        {
            Debug.LogWarning("deactivateA is null!");
        }

        if (deactivateB != null)
        {
            deactivateB.SetActive(false);
            Debug.Log("Deactivated object B");
        }
        else
        {
            Debug.LogWarning("deactivateB is null!");
        }

        Debug.Log("Mini-game completion sequence executed!");
    }

    // Public method to reset the entire mini-game
    public void ResetMiniGame()
    {
        part1Done = false;
        part2Done = false;
        completionButton.interactable = false;

        // Deactivate the both parts completed object
        if (bothPartsCompletedObject != null)
        {
            bothPartsCompletedObject.SetActive(false);
        }

        Debug.Log("Mini-game reset - both parts need to be completed again.");
    }

    // Public method to manually set up button listener (call this if button doesn't work)
    public void SetupButtonListener()
    {
        if (completionButton != null)
        {
            completionButton.onClick.RemoveAllListeners();
            completionButton.onClick.AddListener(OnCompletionButtonClick);
            Debug.Log("Button listener manually set up!");
        }
        else
        {
            Debug.LogError("Cannot set up button listener - completionButton is null!");
        }
    }

    // Public methods to check completion status
    public bool IsPart1Complete() => part1Done;
    public bool IsPart2Complete() => part2Done;
    public bool AreBothPartsComplete() => part1Done && part2Done;
}
