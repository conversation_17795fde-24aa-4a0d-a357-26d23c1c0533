using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using System.Collections;
using System.Collections.Generic;

public class DraggableMaskWithDetection : Mono<PERSON><PERSON><PERSON><PERSON>, IDragHandler
{
    [Header("Mask Settings")]
    public RectTransform maskRect;        // The circular draggable mask
    public RectTransform staticImage;     // The static image that gets revealed by the mask

    [Header("Target Detection")]
    public RectTransform[] targets;       // The small UI elements to detect
    public GameObject[] relatedObjects;   // Objects to activate based on overlap
    [Range(0f, 1f)]
    public float coverageThreshold = 0.8f; // 80% coverage threshold

    [Header("Completion Settings")]
    public GameObject[] objectsToActivateOnCompletion;   // Objects to activate when all targets scanned
    public GameObject[] objectsToDeactivateOnCompletion; // Objects to deactivate when all targets scanned

    [Header("Control")]
    public bool isDraggingEnabled = false; // Controls whether dragging is allowed

    [Header("Animation Settings")]
    public float animationDuration = 2f;   // Duration for the circular frame reveal animation

    [Header("Dragging Settings")]
    [Range(0.1f, 3f)]
    public float draggingSpeed = 1f;       // Speed multiplier for dragging (1 = normal speed)

    private Canvas canvas;
    private bool[] targetScanned;          // Track which targets have been scanned
    private bool[] targetSnapped;          // Track which targets have been snapped to (first time only)
    private HashSet<int> scannedTargets;   // Set of scanned target indices
    private bool allTargetsScanned = false;
    private bool isLockedToTarget = false; // Prevents dragging during animation
    private int currentLockedTarget = -1;  // Index of currently locked target
    private Vector2 dragLimits;            // Calculated drag boundaries
    private bool waitingForLastAnimation = false; // Prevents completion until last animation finishes

    void Start()
    {
        canvas = GetComponentInParent<Canvas>();

        // Initialize tracking arrays
        targetScanned = new bool[targets.Length];
        targetSnapped = new bool[targets.Length];
        scannedTargets = new HashSet<int>();

        // Initially deactivate all related objects
        for (int i = 0; i < relatedObjects.Length; i++)
        {
            if (relatedObjects[i] != null)
                relatedObjects[i].SetActive(false);
        }

        // Calculate drag limits based on static image and mask sizes
        CalculateDragLimits();
    }

    // Call this method from your START BUTTON to enable dragging
    public void StartScanning()
    {
        isDraggingEnabled = true;
        Debug.Log("Scanning started - you can now drag the mask!");
    }

    // Call this method to stop scanning/dragging
    public void StopScanning()
    {
        isDraggingEnabled = false;
        Debug.Log("Scanning stopped");
    }

    // Alternative method names for clarity
    public void EnableDragging()
    {
        StartScanning();
    }

    public void DisableDragging()
    {
        StopScanning();
    }

    void CalculateDragLimits()
    {
        if (staticImage != null && maskRect != null)
        {
            // Get the sizes of both the static image and the mask
            Vector2 staticImageSize = staticImage.rect.size;
            Vector2 maskSize = maskRect.rect.size;

            // Calculate how far the mask can move in each direction
            // The mask should not go beyond the static image boundaries
            dragLimits.x = Mathf.Max(0, (staticImageSize.x - maskSize.x) / 2f);
            dragLimits.y = Mathf.Max(0, (staticImageSize.y - maskSize.y) / 2f);

            Debug.Log($"Drag limits calculated: X = ±{dragLimits.x}, Y = ±{dragLimits.y}");
        }
        else
        {
            Debug.LogWarning("Static image or mask rect not assigned - drag limits set to zero");
            dragLimits = Vector2.zero;
        }
    }

    public void OnDrag(PointerEventData eventData)
    {
        // Only allow dragging if enabled and not locked to a target
        if (!isDraggingEnabled || isLockedToTarget)
            return;

        // Calculate desired movement
        Vector2 deltaMovement = (eventData.delta / canvas.scaleFactor) * draggingSpeed;
        Vector2 newMaskPosition = maskRect.anchoredPosition + deltaMovement;

        // Clamp the mask position to stay within static image boundaries
        newMaskPosition.x = Mathf.Clamp(newMaskPosition.x, -dragLimits.x, dragLimits.x);
        newMaskPosition.y = Mathf.Clamp(newMaskPosition.y, -dragLimits.y, dragLimits.y);

        // Calculate the actual movement after clamping
        Vector2 actualMovement = newMaskPosition - maskRect.anchoredPosition;

        // Apply the clamped movement
        maskRect.anchoredPosition = newMaskPosition;

        // If there's a static image child, move it in the opposite direction
        // This creates the effect of the mask revealing different parts of the image
        if (staticImage != null)
        {
            staticImage.anchoredPosition -= actualMovement;
        }

        CheckCoverage();
    }

    void CheckCoverage()
    {
        for (int i = 0; i < targets.Length; i++)
        {
            float coveragePercentage = CalculateCoveragePercentage(maskRect, targets[i]);

            if (coveragePercentage >= coverageThreshold)
            {
                // Activate related object if not already active
                if (relatedObjects[i] != null && !relatedObjects[i].activeInHierarchy)
                {
                    relatedObjects[i].SetActive(true);
                }

                // Mark as scanned and add to scanned set
                if (!targetScanned[i])
                {
                    targetScanned[i] = true;
                    scannedTargets.Add(i);

                    // Snap to target and start animation on first scan only
                    if (!targetSnapped[i])
                    {
                        StartCoroutine(SnapAndAnimateTarget(i));
                        targetSnapped[i] = true;
                    }
                }
            }
            else
            {
                // Only deactivate related object if it hasn't been fully scanned yet
                // Once a target has been snapped and animated, keep its frame active
                if (!targetSnapped[i] && relatedObjects[i] != null && relatedObjects[i].activeInHierarchy)
                {
                    relatedObjects[i].SetActive(false);
                }

                // Only remove from scanned if it hasn't been snapped yet
                // Once snapped and animated, it should remain permanently scanned
                if (!targetSnapped[i] && targetScanned[i])
                {
                    targetScanned[i] = false;
                    scannedTargets.Remove(i);
                }
            }
        }

        CheckAllTargetsScanned();
    }

    float CalculateCoveragePercentage(RectTransform mask, RectTransform target)
    {
        // Get world positions and sizes
        Vector3[] maskCorners = new Vector3[4];
        Vector3[] targetCorners = new Vector3[4];

        mask.GetWorldCorners(maskCorners);
        target.GetWorldCorners(targetCorners);

        // Convert to screen space for easier calculation
        Vector2 maskCenter = Camera.main.WorldToScreenPoint(mask.position);
        Vector2 targetCenter = Camera.main.WorldToScreenPoint(target.position);

        // Calculate mask radius (assuming circular mask)
        float maskRadius = Vector2.Distance(
            Camera.main.WorldToScreenPoint(maskCorners[0]),
            Camera.main.WorldToScreenPoint(maskCorners[2])
        ) * 0.5f;

        // Calculate target size
        Vector2 targetSize = new Vector2(
            Vector2.Distance(Camera.main.WorldToScreenPoint(targetCorners[0]), Camera.main.WorldToScreenPoint(targetCorners[3])),
            Vector2.Distance(Camera.main.WorldToScreenPoint(targetCorners[0]), Camera.main.WorldToScreenPoint(targetCorners[1]))
        );

        // Calculate overlap area (simplified circular-rectangular intersection)
        float distance = Vector2.Distance(maskCenter, targetCenter);

        // If mask completely contains target
        if (distance + Mathf.Max(targetSize.x, targetSize.y) * 0.5f <= maskRadius)
        {
            return 1.0f; // 100% coverage
        }

        // If no overlap
        if (distance > maskRadius + Mathf.Max(targetSize.x, targetSize.y) * 0.5f)
        {
            return 0.0f; // 0% coverage
        }

        // Approximate coverage calculation
        float maxDistance = maskRadius + Mathf.Max(targetSize.x, targetSize.y) * 0.5f;
        float minDistance = Mathf.Abs(maskRadius - Mathf.Max(targetSize.x, targetSize.y) * 0.5f);

        if (distance <= minDistance)
            return 1.0f;

        // Linear interpolation for partial coverage
        return Mathf.Clamp01(1.0f - (distance - minDistance) / (maxDistance - minDistance));
    }

    IEnumerator SnapAndAnimateTarget(int targetIndex)
    {
        // Lock dragging during animation
        isLockedToTarget = true;
        currentLockedTarget = targetIndex;

        // Snap mask to target position
        SnapToTarget(targets[targetIndex]);

        // Start the circular frame reveal animation
        yield return StartCoroutine(AnimateCircularFrameReveal(targetIndex));

        // Unlock dragging after animation completes
        isLockedToTarget = false;
        currentLockedTarget = -1;

        // Check if this was the last target and we were waiting for completion
        if (waitingForLastAnimation && scannedTargets.Count >= targets.Length)
        {
            waitingForLastAnimation = false;
            allTargetsScanned = true;
            OnAllTargetsScanned();
            Debug.Log($"Target {targetIndex} scanning complete - ALL TARGETS SCANNED!");
        }
        else
        {
            Debug.Log($"Target {targetIndex} scanning complete - dragging resumed!");
        }
    }

    IEnumerator AnimateCircularFrameReveal(int targetIndex)
    {
        GameObject relatedObject = relatedObjects[targetIndex];
        if (relatedObject == null)
        {
            Debug.LogWarning($"Related object for target {targetIndex} is null!");
            yield break;
        }

        // Get the Image component for the circular frame
        Image frameImage = relatedObject.GetComponent<Image>();
        if (frameImage == null)
        {
            Debug.LogWarning($"Related object for target {targetIndex} doesn't have an Image component!");
            yield break;
        }

        // Ensure the object is active
        relatedObject.SetActive(true);

        // Set initial fill amount to 0 (completely hidden)
        frameImage.fillMethod = Image.FillMethod.Radial360;
        frameImage.fillOrigin = (int)Image.Origin360.Top; // Start from top
        frameImage.fillClockwise = true; // Fill clockwise
        frameImage.fillAmount = 0f;

        // Animate the fill amount from 0 to 1 over the specified duration
        float elapsedTime = 0f;

        while (elapsedTime < animationDuration)
        {
            elapsedTime += Time.deltaTime;
            float progress = elapsedTime / animationDuration;

            // Use smooth curve for better visual effect
            float smoothProgress = Mathf.SmoothStep(0f, 1f, progress);
            frameImage.fillAmount = smoothProgress;

            yield return null;
        }

        // Ensure final state
        frameImage.fillAmount = 1f;

        Debug.Log($"Circular frame animation complete for target {targetIndex}");
    }

    void SnapToTarget(RectTransform target)
    {
        // Calculate the movement needed to snap to target
        Vector2 currentMaskPosition = maskRect.anchoredPosition;
        Vector2 targetPosition = target.anchoredPosition;
        Vector2 snapMovement = targetPosition - currentMaskPosition;

        // Snap mask to target position
        maskRect.anchoredPosition = targetPosition;

        // Move the static image in the opposite direction to maintain the reveal effect
        if (staticImage != null)
        {
            staticImage.anchoredPosition -= snapMovement;
        }
    }

    void CheckAllTargetsScanned()
    {
        bool allScanned = scannedTargets.Count >= targets.Length;

        if (allScanned && !allTargetsScanned && !waitingForLastAnimation)
        {
            // Don't trigger completion immediately if we're currently animating a target
            if (isLockedToTarget)
            {
                waitingForLastAnimation = true;
                Debug.Log("All targets scanned - waiting for last animation to complete...");
            }
            else
            {
                allTargetsScanned = true;
                OnAllTargetsScanned();
            }
        }
        else if (!allScanned && allTargetsScanned)
        {
            allTargetsScanned = false;
            waitingForLastAnimation = false;
            OnNotAllTargetsScanned();
        }
    }

    void OnAllTargetsScanned()
    {
        // Activate completion objects
        foreach (GameObject obj in objectsToActivateOnCompletion)
        {
            if (obj != null)
                obj.SetActive(true);
        }

        // Deactivate completion objects
        foreach (GameObject obj in objectsToDeactivateOnCompletion)
        {
            if (obj != null)
                obj.SetActive(false);
        }

        Debug.Log("All targets have been scanned!");
    }

    void OnNotAllTargetsScanned()
    {
        // Reverse the completion state if not all targets are scanned anymore
        foreach (GameObject obj in objectsToActivateOnCompletion)
        {
            if (obj != null)
                obj.SetActive(false);
        }

        foreach (GameObject obj in objectsToDeactivateOnCompletion)
        {
            if (obj != null)
                obj.SetActive(true);
        }
    }
}
