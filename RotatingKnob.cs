using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using UnityEngine.Events;

[System.Serializable]
public class KnobValueChangedEvent : UnityEvent<float> { }

public class RotatingKnob : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointer<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rag<PERSON><PERSON><PERSON>, IPointerUpHandler
{
    [Header("Knob Settings")]
    public RectTransform knobHandle;
    public float minAngle = -150f;
    public float maxAngle = 150f;
    
    [Header("Value Settings")]
    [Range(0f, 1f)]
    public float currentValue = 0.5f;
    public bool snapToSteps = false;
    public int numberOfSteps = 10;
    
    [Header("Visual Feedback")]
    public bool showValueText = true;
    public Text valueText;
    public string valueFormat = "F2";
    
    [Header("Events")]
    public KnobValueChangedEvent OnValueChanged = new KnobValueChangedEvent();
    
    private bool isDragging = false;
    private Vector2 centerPoint;
    private float currentAngle;
    
    void Start()
    {
        if (knobHandle == null)
            knobHandle = GetComponent<RectTransform>();
            
        UpdateKnobVisual();
        UpdateValueText();
    }
    
    public void OnPointerDown(PointerEventData eventData)
    {
        isDragging = true;
        RectTransformUtility.ScreenPointToLocalPointInRectangle(
            transform as RectTransform, 
            eventData.position, 
            eventData.pressEventCamera, 
            out centerPoint);
    }
    
    public void OnDrag(PointerEventData eventData)
    {
        if (!isDragging) return;
        
        Vector2 localPoint;
        RectTransformUtility.ScreenPointToLocalPointInRectangle(
            transform as RectTransform, 
            eventData.position, 
            eventData.pressEventCamera, 
            out localPoint);
        
        // Calculate angle from center
        Vector2 direction = localPoint - centerPoint;
        float angle = Mathf.Atan2(direction.y, direction.x) * Mathf.Rad2Deg;
        
        // Adjust angle to match our knob range
        angle = NormalizeAngle(angle);
        
        // Clamp angle to our min/max range
        angle = Mathf.Clamp(angle, minAngle, maxAngle);
        
        // Convert angle to value (0-1)
        float newValue = Mathf.InverseLerp(minAngle, maxAngle, angle);
        
        // Apply stepping if enabled
        if (snapToSteps)
        {
            float step = 1f / (numberOfSteps - 1);
            newValue = Mathf.Round(newValue / step) * step;
        }
        
        SetValue(newValue);
    }
    
    public void OnPointerUp(PointerEventData eventData)
    {
        isDragging = false;
    }
    
    public void SetValue(float value)
    {
        value = Mathf.Clamp01(value);
        
        if (Mathf.Approximately(currentValue, value))
            return;
            
        currentValue = value;
        UpdateKnobVisual();
        UpdateValueText();
        OnValueChanged.Invoke(currentValue);
    }
    
    private void UpdateKnobVisual()
    {
        if (knobHandle == null) return;
        
        float angle = Mathf.Lerp(minAngle, maxAngle, currentValue);
        knobHandle.localEulerAngles = new Vector3(0, 0, angle);
        currentAngle = angle;
    }
    
    private void UpdateValueText()
    {
        if (showValueText && valueText != null)
        {
            valueText.text = currentValue.ToString(valueFormat);
        }
    }
    
    private float NormalizeAngle(float angle)
    {
        // Convert angle to our coordinate system
        // Unity's UI uses different angle conventions
        angle = -angle + 90f; // Adjust for UI coordinate system
        
        while (angle > 180f) angle -= 360f;
        while (angle < -180f) angle += 360f;
        
        return angle;
    }
    
    // Public method to get current value
    public float GetValue()
    {
        return currentValue;
    }
    
    // Method to set value without triggering events (useful for initialization)
    public void SetValueWithoutNotify(float value)
    {
        currentValue = Mathf.Clamp01(value);
        UpdateKnobVisual();
        UpdateValueText();
    }
}
