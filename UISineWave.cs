using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

[RequireComponent(typeof(RectTransform))]
public class UISineWave : MaskableGraphic
{
    [Header("Wave Properties")]
    public float frequency = 1f;
    public float magnitude = 1f;
    public float lineThickness = 2f;
    public Color waveColor = Color.green;
    
    [Header("Wave Settings")]
    public int resolution = 100;
    public float waveLength = 2f * Mathf.PI;
    
    private List<Vector2> wavePoints = new List<Vector2>();
    
    protected override void OnPopulateMesh(VertexHelper vh)
    {
        vh.Clear();
        
        if (resolution < 2) return;
        
        // Calculate wave points
        CalculateWavePoints();
        
        // Draw the wave using line segments
        for (int i = 0; i < wavePoints.Count - 1; i++)
        {
            DrawLineSegment(vh, wavePoints[i], wavePoints[i + 1], lineThickness, waveColor);
        }
    }
    
    private void CalculateWavePoints()
    {
        wavePoints.Clear();
        
        RectTransform rect = rectTransform;
        float width = rect.rect.width;
        float height = rect.rect.height;
        
        for (int i = 0; i < resolution; i++)
        {
            // Calculate x position across the width
            float t = (float)i / (resolution - 1); // 0 to 1
            float x = t * width - width * 0.5f; // Center around 0
            
            // Calculate sine wave y position
            float waveX = t * waveLength; // Map to wave length
            float y = Mathf.Sin(waveX * frequency) * magnitude * height * 0.25f; // Scale to UI
            
            wavePoints.Add(new Vector2(x, y));
        }
    }
    
    private void DrawLineSegment(VertexHelper vh, Vector2 start, Vector2 end, float thickness, Color color)
    {
        Vector2 direction = (end - start).normalized;
        Vector2 perpendicular = new Vector2(-direction.y, direction.x) * thickness * 0.5f;
        
        // Create quad vertices for the line segment
        UIVertex[] vertices = new UIVertex[4];
        
        vertices[0] = CreateVertex(start - perpendicular, color);
        vertices[1] = CreateVertex(start + perpendicular, color);
        vertices[2] = CreateVertex(end + perpendicular, color);
        vertices[3] = CreateVertex(end - perpendicular, color);
        
        vh.AddUIVertexQuad(vertices);
    }
    
    private UIVertex CreateVertex(Vector2 position, Color color)
    {
        UIVertex vertex = UIVertex.simpleVert;
        vertex.position = position;
        vertex.color = color;
        return vertex;
    }
    
    // Public methods to update wave properties
    public void SetFrequency(float newFrequency)
    {
        if (frequency != newFrequency)
        {
            frequency = newFrequency;
            SetVerticesDirty();
        }
    }
    
    public void SetMagnitude(float newMagnitude)
    {
        if (magnitude != newMagnitude)
        {
            magnitude = newMagnitude;
            SetVerticesDirty();
        }
    }
    
    public void SetWaveProperties(float newFrequency, float newMagnitude)
    {
        if (frequency != newFrequency || magnitude != newMagnitude)
        {
            frequency = newFrequency;
            magnitude = newMagnitude;
            SetVerticesDirty();
        }
    }
    
    public void SetColor(Color newColor)
    {
        if (waveColor != newColor)
        {
            waveColor = newColor;
            SetVerticesDirty();
        }
    }
    
    // Get current wave properties
    public float GetFrequency() => frequency;
    public float GetMagnitude() => magnitude;
    
    // Force refresh the wave
    public void RefreshWave()
    {
        SetVerticesDirty();
    }
    
    // Override color property to use waveColor
    public override Color color
    {
        get { return waveColor; }
        set 
        { 
            if (waveColor != value)
            {
                waveColor = value;
                SetVerticesDirty();
            }
        }
    }
}
