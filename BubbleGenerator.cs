using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Generates animated floating bubbles over a UI tank element
/// Features object pooling, customizable parameters, and smooth animations
/// </summary>
public class BubbleGenerator : MonoBehaviour
{
    [Header("Bubble Configuration")]
    [SerializeField] private GameObject bubblePrefab;
    [SerializeField] private RectTransform tankArea;
    
    [Header("Spawn Settings")]
    [SerializeField] private Vector2 spawnInterval = new Vector2(0.2f, 0.5f);
    [SerializeField] private int maxActiveBubbles = 20;
    
    [Header("Bubble Properties")]
    [SerializeField] private Vector2 bubbleSpeed = new Vector2(50f, 100f);
    [SerializeField] private Vector2 bubbleSize = new Vector2(10f, 30f);
    [SerializeField] private Vector2 bubbleAlpha = new Vector2(0.3f, 0.6f);
    
    [Header("Movement Settings")]
    [SerializeField] private float horizontalDrift = 20f;
    [SerializeField] private float sineWaveAmplitude = 10f;
    [SerializeField] private float sineWaveFrequency = 1f;
    [SerializeField] private bool fadeOutAtTop = true;
    [SerializeField] private float fadeOutDistance = 50f;
    [SerializeField] private bool bounceOffWalls = false;
    
    // Object pooling
    private Queue<GameObject> bubblePool = new Queue<GameObject>();
    private List<BubbleController> activeBubbles = new List<BubbleController>();
    
    // Spawning
    private Coroutine spawnCoroutine;
    private bool isSpawning = false;
    
    private void Start()
    {
        // Debug validation
        if (bubblePrefab == null)
        {
            Debug.LogError("BubbleGenerator: bubblePrefab is not assigned in the Inspector!");
            return;
        }

        if (tankArea == null)
        {
            Debug.LogError("BubbleGenerator: tankArea is not assigned in the Inspector!");
            return;
        }

        Debug.Log($"BubbleGenerator: Starting with tank area size: {tankArea.rect.size}");

        InitializeBubblePool();
        StartSpawning();
    }
    
    private void Update()
    {
        UpdateActiveBubbles();
    }
    
    /// <summary>
    /// Initialize the object pool with bubble instances
    /// </summary>
    private void InitializeBubblePool()
    {
        if (bubblePrefab == null || tankArea == null)
        {
            Debug.LogError("BubbleGenerator: Bubble prefab or tank area not assigned!");
            return;
        }
        
        // Pre-instantiate bubbles for the pool
        for (int i = 0; i < maxActiveBubbles; i++)
        {
            GameObject bubble = Instantiate(bubblePrefab, tankArea);
            bubble.SetActive(false);
            
            // Add BubbleController component if it doesn't exist
            if (bubble.GetComponent<BubbleController>() == null)
            {
                bubble.AddComponent<BubbleController>();
            }
            
            bubblePool.Enqueue(bubble);
        }
    }
    
    /// <summary>
    /// Start the bubble spawning coroutine
    /// </summary>
    public void StartSpawning()
    {
        if (!isSpawning)
        {
            isSpawning = true;
            spawnCoroutine = StartCoroutine(SpawnBubbles());
        }
    }
    
    /// <summary>
    /// Stop the bubble spawning coroutine
    /// </summary>
    public void StopSpawning()
    {
        if (isSpawning)
        {
            isSpawning = false;
            if (spawnCoroutine != null)
            {
                StopCoroutine(spawnCoroutine);
            }
        }
    }
    
    /// <summary>
    /// Coroutine that handles bubble spawning at random intervals
    /// </summary>
    private IEnumerator SpawnBubbles()
    {
        while (isSpawning)
        {
            if (activeBubbles.Count < maxActiveBubbles && bubblePool.Count > 0)
            {
                SpawnBubble();
            }
            
            float waitTime = Random.Range(spawnInterval.x, spawnInterval.y);
            yield return new WaitForSeconds(waitTime);
        }
    }
    
    /// <summary>
    /// Spawn a single bubble from the pool
    /// </summary>
    private void SpawnBubble()
    {
        if (bubblePool.Count == 0) return;

        GameObject bubble = bubblePool.Dequeue();
        BubbleController controller = bubble.GetComponent<BubbleController>();

        if (controller == null)
        {
            Debug.LogError("BubbleGenerator: No BubbleController found on bubble!");
            return;
        }

        // Activate bubble first so components work properly
        bubble.SetActive(true);

        // Configure bubble properties
        ConfigureBubble(bubble, controller);

        // Add to active list
        activeBubbles.Add(controller);

        // Debug logging
        Debug.Log($"BubbleGenerator: Spawned bubble at position {bubble.GetComponent<RectTransform>().anchoredPosition}, " +
                  $"size {bubble.GetComponent<RectTransform>().sizeDelta}, " +
                  $"alpha {bubble.GetComponent<Image>().color.a}, " +
                  $"active: {bubble.activeInHierarchy}");
    }
    
    /// <summary>
    /// Configure bubble appearance and movement properties
    /// </summary>
    private void ConfigureBubble(GameObject bubble, BubbleController controller)
    {
        RectTransform bubbleRect = bubble.GetComponent<RectTransform>();
        Image bubbleImage = bubble.GetComponent<Image>();

        if (bubbleRect == null)
        {
            Debug.LogError("BubbleGenerator: No RectTransform found on bubble!");
            return;
        }

        if (bubbleImage == null)
        {
            Debug.LogError("BubbleGenerator: No Image component found on bubble!");
            return;
        }

        // Set random size
        float size = Random.Range(bubbleSize.x, bubbleSize.y);
        bubbleRect.sizeDelta = new Vector2(size, size);

        // Set random alpha
        Color color = bubbleImage.color;
        color.a = Random.Range(bubbleAlpha.x, bubbleAlpha.y);
        bubbleImage.color = color;

        // Set spawn position (random X at bottom of tank)
        Vector2 tankSize = tankArea.rect.size;
        float randomX = Random.Range(-tankSize.x * 0.4f, tankSize.x * 0.4f);
        float startY = -tankSize.y * 0.5f;

        Vector2 spawnPosition = new Vector2(randomX, startY);
        bubbleRect.anchoredPosition = spawnPosition;

        // Configure movement
        float speed = Random.Range(bubbleSpeed.x, bubbleSpeed.y);
        float drift = Random.Range(-horizontalDrift, horizontalDrift);

        // Initialize controller with movement parameters
        controller.Initialize(speed, drift, sineWaveAmplitude, sineWaveFrequency,
                            fadeOutAtTop, fadeOutDistance, tankSize.y, tankSize.x, bounceOffWalls);

        Debug.Log($"BubbleGenerator: Configured bubble - Size: {size}, Alpha: {color.a}, " +
                  $"Position: {spawnPosition}, Speed: {speed}, Tank Size: {tankSize}");
    }
    
    /// <summary>
    /// Update all active bubbles and handle cleanup
    /// </summary>
    private void UpdateActiveBubbles()
    {
        for (int i = activeBubbles.Count - 1; i >= 0; i--)
        {
            BubbleController bubble = activeBubbles[i];
            
            if (bubble == null || !bubble.gameObject.activeInHierarchy)
            {
                activeBubbles.RemoveAt(i);
                continue;
            }
            
            // Check if bubble has reached the top
            RectTransform bubbleRect = bubble.GetComponent<RectTransform>();
            float tankHeight = tankArea.rect.height;
            
            if (bubbleRect.anchoredPosition.y > tankHeight * 0.5f + 50f)
            {
                ReturnBubbleToPool(bubble.gameObject, i);
            }
        }
    }
    
    /// <summary>
    /// Return a bubble to the object pool
    /// </summary>
    private void ReturnBubbleToPool(GameObject bubble, int activeIndex)
    {
        bubble.SetActive(false);
        activeBubbles.RemoveAt(activeIndex);
        bubblePool.Enqueue(bubble);
    }
    
    /// <summary>
    /// Clear all active bubbles (useful for cleanup)
    /// </summary>
    public void ClearAllBubbles()
    {
        for (int i = activeBubbles.Count - 1; i >= 0; i--)
        {
            if (activeBubbles[i] != null)
            {
                ReturnBubbleToPool(activeBubbles[i].gameObject, i);
            }
        }
    }
    
    private void OnDestroy()
    {
        StopSpawning();
    }
    
    private void OnValidate()
    {
        // Ensure valid ranges
        if (spawnInterval.x > spawnInterval.y)
            spawnInterval.y = spawnInterval.x;
        
        if (bubbleSpeed.x > bubbleSpeed.y)
            bubbleSpeed.y = bubbleSpeed.x;
        
        if (bubbleSize.x > bubbleSize.y)
            bubbleSize.y = bubbleSize.x;
        
        if (bubbleAlpha.x > bubbleAlpha.y)
            bubbleAlpha.y = bubbleAlpha.x;
        
        // Clamp alpha values
        bubbleAlpha.x = Mathf.Clamp01(bubbleAlpha.x);
        bubbleAlpha.y = Mathf.Clamp01(bubbleAlpha.y);
    }
}
