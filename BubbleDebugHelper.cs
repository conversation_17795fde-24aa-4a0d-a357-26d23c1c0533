using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Debug helper script to test bubble visibility issues
/// Attach this to a GameObject in your scene and assign the bubble prefab
/// </summary>
public class BubbleDebugHelper : MonoBehaviour
{
    [Header("Debug Settings")]
    [SerializeField] private GameObject bubblePrefab;
    [SerializeField] private RectTransform testArea;
    [SerializeField] private bool createTestBubble = false;
    [SerializeField] private bool logCanvasInfo = false;
    
    private GameObject testBubble;
    
    private void Start()
    {
        if (logCanvasInfo)
        {
            LogCanvasHierarchy();
        }
    }
    
    private void Update()
    {
        if (createTestBubble && bubblePrefab != null && testArea != null)
        {
            CreateTestBubble();
            createTestBubble = false;
        }
    }
    
    /// <summary>
    /// Create a single test bubble to check visibility
    /// </summary>
    private void CreateTestBubble()
    {
        // Clean up previous test bubble
        if (testBubble != null)
        {
            DestroyImmediate(testBubble);
        }
        
        // Create new test bubble
        testBubble = Instantiate(bubblePrefab, testArea);
        
        // Configure for visibility testing
        RectTransform bubbleRect = testBubble.GetComponent<RectTransform>();
        Image bubbleImage = testBubble.GetComponent<Image>();
        
        // Set large size and full alpha for visibility
        bubbleRect.sizeDelta = new Vector2(50f, 50f);
        bubbleRect.anchoredPosition = Vector2.zero; // Center of test area
        
        if (bubbleImage != null)
        {
            Color color = bubbleImage.color;
            color.a = 1f; // Full alpha
            bubbleImage.color = color;
            
            Debug.Log($"BubbleDebugHelper: Created test bubble - " +
                      $"Position: {bubbleRect.anchoredPosition}, " +
                      $"Size: {bubbleRect.sizeDelta}, " +
                      $"Color: {bubbleImage.color}, " +
                      $"Sprite: {bubbleImage.sprite?.name ?? "NULL"}, " +
                      $"Active: {testBubble.activeInHierarchy}");
        }
        else
        {
            Debug.LogError("BubbleDebugHelper: No Image component found on bubble prefab!");
        }
        
        // Log hierarchy info
        LogBubbleHierarchy(testBubble);
    }
    
    /// <summary>
    /// Log canvas hierarchy information
    /// </summary>
    private void LogCanvasHierarchy()
    {
        Canvas[] canvases = FindObjectsOfType<Canvas>();
        Debug.Log($"BubbleDebugHelper: Found {canvases.Length} canvases in scene:");
        
        for (int i = 0; i < canvases.Length; i++)
        {
            Canvas canvas = canvases[i];
            Debug.Log($"Canvas {i}: {canvas.name} - " +
                      $"Sort Order: {canvas.sortingOrder}, " +
                      $"Render Mode: {canvas.renderMode}, " +
                      $"Pixel Perfect: {canvas.pixelPerfect}");
        }
    }
    
    /// <summary>
    /// Log bubble hierarchy and component information
    /// </summary>
    private void LogBubbleHierarchy(GameObject bubble)
    {
        Transform current = bubble.transform;
        string hierarchy = bubble.name;
        
        while (current.parent != null)
        {
            current = current.parent;
            hierarchy = current.name + "/" + hierarchy;
        }
        
        Debug.Log($"BubbleDebugHelper: Bubble hierarchy: {hierarchy}");
        
        // Check for Canvas components in hierarchy
        current = bubble.transform;
        while (current != null)
        {
            Canvas canvas = current.GetComponent<Canvas>();
            if (canvas != null)
            {
                Debug.Log($"BubbleDebugHelper: Found Canvas on {current.name} - Sort Order: {canvas.sortingOrder}");
                break;
            }
            current = current.parent;
        }
        
        // Log all components on the bubble
        Component[] components = bubble.GetComponents<Component>();
        Debug.Log($"BubbleDebugHelper: Bubble components: {string.Join(", ", System.Array.ConvertAll(components, c => c.GetType().Name))}");
    }
    
    /// <summary>
    /// Manual test function you can call from inspector or other scripts
    /// </summary>
    [ContextMenu("Create Test Bubble")]
    public void CreateTestBubbleManual()
    {
        createTestBubble = true;
    }
    
    /// <summary>
    /// Clear the test bubble
    /// </summary>
    [ContextMenu("Clear Test Bubble")]
    public void ClearTestBubble()
    {
        if (testBubble != null)
        {
            DestroyImmediate(testBubble);
            testBubble = null;
        }
    }
}
