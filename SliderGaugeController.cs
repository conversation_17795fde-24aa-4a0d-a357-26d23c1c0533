using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class SliderGaugeController : MonoBehaviour
{
    [Head<PERSON>("UI Elements")]
    public Slider[] sliders; // size 3
    public RectTransform gaugeNeedle;
    public GameObject sliderSuccessObject;

    [Header("Target Display (Optional)")]
    public TextMeshProUGUI[] targetValueTexts; // Optional: to show target values to player

    [<PERSON><PERSON>("Settings")]
    public float maxDelta = 5f; // Adjusted for 0-100 range
    public bool showTargetValues = false; // Set to true if you want to show targets

    private float[] targets = new float[3];
    private bool isLocked = false;

    void Start()
    {
        // Generate random target values as whole numbers (0-100)
        for (int i = 0; i < 3; i++)
        {
            // Set slider ranges to 0-100 and generate whole number targets
            sliders[i].minValue = 0f;
            sliders[i].maxValue = 100f;
            sliders[i].wholeNumbers = true;

            targets[i] = Random.Range(0, 101); // 0 to 100 inclusive
            Debug.Log($"Slider {i} target value: {targets[i]:F0}");
        }

        // Update target value display if enabled
        UpdateTargetDisplay();

        // Connect slider events to OnSliderChanged method
        for (int i = 0; i < sliders.Length; i++)
        {
            if (sliders[i] != null)
            {
                int index = i; // Capture index for lambda
                sliders[i].onValueChanged.AddListener(delegate { OnSliderChanged(index); });
            }
        }

        // Initial needle position update
        OnSliderChanged(-1); // -1 indicates initial call
    }

    public void OnSliderChanged(int changedSliderIndex = -1)
    {
        // If locked, don't allow further changes
        if (isLocked && changedSliderIndex >= 0)
        {
            return;
        }

        float totalMatch = 0f;

        for (int i = 0; i < 3; i++)
        {
            float delta = Mathf.Abs(sliders[i].value - targets[i]);
            float closeness = Mathf.Clamp01(1f - (delta / maxDelta)); // 0 to 1
            totalMatch += closeness;
        }

        float avgMatch = totalMatch / 3f;
        float needleAngle = Mathf.Lerp(0, -180, avgMatch);
        gaugeNeedle.localEulerAngles = new Vector3(0, 0, needleAngle);

        Debug.Log($"Average match: {avgMatch:F2}, Needle angle: {needleAngle:F1}°");

        if (avgMatch >= 0.99f && !isLocked)
        {
            LockNeedle();
            sliderSuccessObject.SetActive(true);
            GaugeGameProgression.Instance.CompletePart(2);
        }
        else if (!isLocked)
        {
            sliderSuccessObject.SetActive(false);
        }
    }

    private void UpdateTargetDisplay()
    {
        if (showTargetValues && targetValueTexts != null)
        {
            for (int i = 0; i < Mathf.Min(targets.Length, targetValueTexts.Length); i++)
            {
                if (targetValueTexts[i] != null)
                {
                    targetValueTexts[i].text = $"Target: {targets[i]:F0}";
                }
            }
        }
    }

    private void LockNeedle()
    {
        isLocked = true;

        // Disable all sliders to prevent further interaction
        for (int i = 0; i < sliders.Length; i++)
        {
            if (sliders[i] != null)
            {
                sliders[i].interactable = false;
            }
        }

        // Ensure needle is at exactly -180 degrees
        gaugeNeedle.localEulerAngles = new Vector3(0, 0, -180);

        Debug.Log("Needle locked at maximum position!");
    }

    // Public method to unlock the needle (useful for resetting the mini-game)
    public void UnlockNeedle()
    {
        isLocked = false;

        // Re-enable all sliders
        for (int i = 0; i < sliders.Length; i++)
        {
            if (sliders[i] != null)
            {
                sliders[i].interactable = true;
            }
        }

        Debug.Log("Needle unlocked!");
    }

    // Public method to reset the mini-game
    public void ResetGame()
    {
        UnlockNeedle();

        // Generate new random targets
        for (int i = 0; i < 3; i++)
        {
            targets[i] = Random.Range(0, 101);
            sliders[i].value = Random.Range(0, 101); // Set random starting positions
        }

        UpdateTargetDisplay();
        sliderSuccessObject.SetActive(false);
        OnSliderChanged(-1); // Update needle position
    }

    // Public method to get target values (useful for debugging or UI display)
    public float[] GetTargetValues()
    {
        return (float[])targets.Clone();
    }

    // Public method to check if needle is locked
    public bool IsLocked()
    {
        return isLocked;
    }
}
