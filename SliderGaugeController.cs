using UnityEngine;
using UnityEngine.UI;

public class SliderGaugeController : MonoBehaviour
{
    public Slider[] sliders; // size 3
    public RectTransform gaugeNeedle;
    public GameObject sliderSuccessObject;

    private float[] targets = new float[3];
    private float maxDelta = 0.05f;

    void Start()
    {
        for (int i = 0; i < 3; i++)
        {
            targets[i] = Random.Range(sliders[i].minValue, sliders[i].maxValue);
        }
    }

    public void OnSliderChanged()
    {
        float totalMatch = 0f;

        for (int i = 0; i < 3; i++)
        {
            float delta = Mathf.Abs(sliders[i].value - targets[i]);
            float closeness = Mathf.Clamp01(1f - (delta / maxDelta)); // 0 to 1
            totalMatch += closeness;
        }

        float avgMatch = totalMatch / 3f;
        gaugeNeedle.localEulerAngles = new Vector3(0, 0, Mathf.Lerp(0, -180, avgMatch));

        if (avgMatch >= 0.99f)
        {
            sliderSuccessObject.SetActive(true);
            GaugeGameProgression.Instance.CompletePart(2);
        }
        else
        {
            sliderSuccessObject.SetActive(false);
        }
    }
}
