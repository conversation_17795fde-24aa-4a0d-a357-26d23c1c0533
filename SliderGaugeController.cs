using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class SliderGaugeController : MonoBehaviour
{
    [Header("UI Elements")]
    public Slider[] sliders; // size 3
    public RectTransform gaugeNeedle;
    public GameObject sliderSuccessObject;

    [Header("Target Display (Optional)")]
    public TextMeshProUGUI[] targetValueTexts; // Optional: to show target values to player

    [<PERSON><PERSON>("Settings")]
    public float maxDelta = 5f; // Adjusted for 0-100 range
    public bool showTargetValues = false; // Set to true if you want to show targets

    private float[] targets = new float[3];

    void Start()
    {
        // Generate random target values as whole numbers (0-100)
        for (int i = 0; i < 3; i++)
        {
            // Set slider ranges to 0-100 and generate whole number targets
            sliders[i].minValue = 0f;
            sliders[i].maxValue = 100f;
            sliders[i].wholeNumbers = true;

            targets[i] = Random.Range(0, 101); // 0 to 100 inclusive
            Debug.Log($"Slider {i} target value: {targets[i]:F0}");
        }

        // Update target value display if enabled
        UpdateTargetDisplay();

        // Connect slider events to OnSliderChanged method
        for (int i = 0; i < sliders.Length; i++)
        {
            if (sliders[i] != null)
            {
                sliders[i].onValueChanged.AddListener(delegate { OnSliderChanged(); });
            }
        }

        // Initial needle position update
        OnSliderChanged();
    }

    public void OnSliderChanged()
    {
        float totalMatch = 0f;

        for (int i = 0; i < 3; i++)
        {
            float delta = Mathf.Abs(sliders[i].value - targets[i]);
            float closeness = Mathf.Clamp01(1f - (delta / maxDelta)); // 0 to 1
            totalMatch += closeness;
        }

        float avgMatch = totalMatch / 3f;
        float needleAngle = Mathf.Lerp(0, -180, avgMatch);
        gaugeNeedle.localEulerAngles = new Vector3(0, 0, needleAngle);

        Debug.Log($"Average match: {avgMatch:F2}, Needle angle: {needleAngle:F1}°");

        if (avgMatch >= 0.99f)
        {
            sliderSuccessObject.SetActive(true);
            GaugeGameProgression.Instance.CompletePart(2);
        }
        else
        {
            sliderSuccessObject.SetActive(false);
        }
    }

    private void UpdateTargetDisplay()
    {
        if (showTargetValues && targetValueTexts != null)
        {
            for (int i = 0; i < Mathf.Min(targets.Length, targetValueTexts.Length); i++)
            {
                if (targetValueTexts[i] != null)
                {
                    targetValueTexts[i].text = $"Target: {targets[i]:F0}";
                }
            }
        }
    }

    // Public method to get target values (useful for debugging or UI display)
    public float[] GetTargetValues()
    {
        return (float[])targets.Clone();
    }
}
