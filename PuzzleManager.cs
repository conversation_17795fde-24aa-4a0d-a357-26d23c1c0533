using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

public class PuzzleManager : MonoBehaviour
{
    public static PuzzleManager Instance;

    public List<Sprite> allPieces;
    public GameObject piecePrefab;
    public Transform scrollContent;
    public Transform puzzleGrid;
    public Button startButton;
    public GameObject previewImage; // The full puzzle preview image

    [Header("Puzzle Completion Effects")]
    public GameObject[] objectsToDeactivateOnCompletion; // Objects to set inactive when puzzle is completed
    public GameObject[] objectsToActivateOnCompletion; // Objects to set active when puzzle is completed

    private int correctCount = 0;
    private bool gameStarted = false;

    public bool GameStarted => gameStarted;

    void Awake()
    {
        Instance = this;
    }

    void Start()
    {
        GeneratePuzzle();

        // Setup start button
        if (startButton != null)
        {
            startButton.onClick.AddListener(StartGame);
        }
    }

    public void StartGame()
    {
        gameStarted = true;

        // Hide preview image
        if (previewImage != null)
        {
            previewImage.SetActive(false);
        }

        // Hide start button
        if (startButton != null)
        {
            startButton.gameObject.SetActive(false);
        }
    }

    void GeneratePuzzle()
    {
        // 1. Use existing puzzle slots instead of creating new ones
        PuzzleSlot[] existingSlots = puzzleGrid.GetComponentsInChildren<PuzzleSlot>();

        // Make sure we have the right number of slots
        if (existingSlots.Length != allPieces.Count)
        {
            Debug.LogError($"Mismatch: {allPieces.Count} pieces but {existingSlots.Length} slots!");
            return;
        }

        // 2. Shuffle and populate ScrollView
        List<Sprite> shuffled = new List<Sprite>(allPieces);
        Shuffle(shuffled);

        for (int i = 0; i < shuffled.Count; i++)
        {
            GameObject piece = Instantiate(piecePrefab, scrollContent);
            piece.GetComponent<Image>().sprite = shuffled[i];
            piece.GetComponent<PuzzlePiece>().pieceIndex = allPieces.IndexOf(shuffled[i]);
        }
    }

    public void RegisterCorrectPiece()
    {
        correctCount++;
        if (correctCount >= allPieces.Count)
        {
            Debug.Log("🎉 Puzzle Completed!");
            OnPuzzleCompleted();
        }
    }

    private void OnPuzzleCompleted()
    {
        // Deactivate specified objects
        foreach (GameObject obj in objectsToDeactivateOnCompletion)
        {
            if (obj != null)
            {
                obj.SetActive(false);
            }
        }

        // Activate specified objects
        foreach (GameObject obj in objectsToActivateOnCompletion)
        {
            if (obj != null)
            {
                obj.SetActive(true);
            }
        }
    }

    void Shuffle(List<Sprite> list)
    {
        for (int i = 0; i < list.Count; i++)
        {
            int rand = Random.Range(i, list.Count);
            Sprite temp = list[i];
            list[i] = list[rand];
            list[rand] = temp;
        }
    }
}
